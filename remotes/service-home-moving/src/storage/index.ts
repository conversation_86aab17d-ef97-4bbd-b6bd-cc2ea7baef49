import {
  AppStorage,
  createZustand,
  GenderMassage,
  HomeMovingProgressPostTaskType,
  IAddons,
  IAddress,
  IDate,
  IDetailHomeMoving,
  IFurnitureItem,
  IHomeDetail,
  IHomeTypeHomeMoving,
  IOptionAreaOfficeCleaning,
  IOptionHomeDetail,
  IOptionItem,
  IPackageMassageItem,
  IPackageOption,
  IPrice,
  IService,
  IUser,
  IUserLocation,
  IVatInfo,
  MassagePostTaskType,
  Maybe,
  Requirement,
  TypeExecutionOrder,
  TypeNumberOfPeopleMassage,
} from '@btaskee/design-system';
import { createJSONStorage, persist } from 'zustand/middleware';

interface AppState {
  address: IAddress;
  duration: number;
  requirements: Requirement[];
  isPremium: boolean;
  isAutoChooseTasker: boolean;
  isFavouriteTasker: boolean;
  gender: string;
  pet: any;
  addons: IAddons[];
  date: Maybe<IDate>;
  schedule: string[];
  isEnabledSchedule: boolean;
  note: string;
  isApplyNoteForAllTask: boolean;
  price: Maybe<IPrice>;
  service: Maybe<IService>;
  paymentMethod: any;
  promotion: any;
  isLoadingPrice: boolean;
  loadingPostTask: boolean;
  homeNumber: string;
  weekdays: number[];
  isFirstOpen: boolean;
  detailOfficeCleaning: Maybe<IOptionAreaOfficeCleaning>;
  vatInfo: Maybe<IVatInfo>;
  isUpdateVatInfoToUser: boolean;
  forceTasker: Maybe<IUser>;
  dateOptions: Maybe<IDate[]>;

  // Home moving-specific state
  currentStep: HomeMovingProgressPostTaskType;
  passStep: HomeMovingProgressPostTaskType;
  oldHomeDetail?: Maybe<IHomeDetail>;
  newHomeDetail?: Maybe<IHomeDetail>;
  isInBuilding?: Maybe<IDetailHomeMoving['isInBuilding']>;
  isCanMoveInBuilding?: Maybe<IHomeTypeHomeMoving['isCanMoveInBuilding']>;
  temptStairsTransportStep1?: Maybe<IOptionHomeDetail>;
  furniture?: Maybe<IFurnitureItem[]>;
  locations?: Maybe<IUserLocation[]>;

  setAddress: (address: IAddress) => void;
  setDuration: (duration: number) => void;
  setRequirements: (requirements: Requirement[]) => void;
  setIsPremium: (isPremium: boolean) => void;
  setIsAutoChooseTasker: (isAutoChooseTasker: boolean) => void;
  setIsFavouriteTasker: (isFavouriteTasker: boolean) => void;
  setGender: (gender: string) => void;
  setAddons: (addons: IAddons[]) => void;
  setPet: (pet: any) => void;
  setDateTime: (date?: IDate) => void;
  setSchedule: (schedule?: string[]) => void;
  setNote: (note: string) => void;
  setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) => void;
  setPrice: (price?: Maybe<IPrice>) => void;
  setHomeNumber: (homeNumber: string) => void;
  setPaymentMethod: (paymentMethod: any) => void;
  setPromotion: (promotion: any) => void;
  setLoadingPrice: (isLoadingPrice: boolean) => void;
  setService: (service: IService) => void;
  setLoadingPostTask: (loadingPostTask: boolean) => void;
  setWeekdays: (weekdays?: number[]) => void;
  setIsFirstOpen: () => void;
  setDetailOfficeCleaning: (detail: IOptionAreaOfficeCleaning) => void;
  setVatInfo: (vatInfo: IVatInfo) => void;
  setIsUpdateVatInfoToUser: (isUpdateVatInfoToUser: boolean) => void;
  setForceTasker: (forceTasker: Maybe<IUser>) => void;
  setDateOptions: (dateOptions: IDate[]) => void;

  // Home moving-specific setters
  setHomeDetail: ({
    step,
    homeDetail,
  }: {
    step: HomeMovingProgressPostTaskType;
    homeDetail: IHomeDetail;
  }) => void;
  setCurrentStep: (step: HomeMovingProgressPostTaskType) => void;
  setPassStep: (step: HomeMovingProgressPostTaskType) => void;
  setOldHomeDetail: (oldHomeDetail: IHomeDetail) => void;
  setNewHomeDetail: (newHomeDetail: IHomeDetail) => void;
  setIsInBuilding: (isInBuilding: IDetailHomeMoving['isInBuilding']) => void;
  setIsCanMoveInBuilding: (
    isCanMoveInBuilding: IHomeTypeHomeMoving['isCanMoveInBuilding'],
  ) => void;
  setTemptStairsTransportStep1: (payload?: Maybe<IOptionHomeDetail>) => void;
  setFurniture: (furniture: IFurnitureItem[]) => void;
  addLocation: (location: IUserLocation) => void;
  resetHomeMovingState: () => void;
  resetState: () => void;
}

export const usePostTaskStore = createZustand<AppState>()(
  persist(
    (set) => ({
      address: {},
      duration: 0,
      requirements: [],
      isPremium: false,
      isAutoChooseTasker: true,
      isFavouriteTasker: false,
      gender: '',
      pet: '',
      addons: [],
      date: null,
      schedule: [],
      isEnabledSchedule: false,
      note: '',
      isApplyNoteForAllTask: false,
      homeNumber: '',
      price: null,
      service: null,
      paymentMethod: null,
      promotion: null,
      isLoadingPrice: false,
      loadingPostTask: false,
      relatedTask: null,
      weekdays: [],
      isFirstOpen: true,
      detailOfficeCleaning: null,
      vatInfo: null,
      isUpdateVatInfoToUser: false,
      forceTasker: null,
      dateOptions: null,

      // Home moving-specific initial state
      currentStep: HomeMovingProgressPostTaskType.Step1,
      passStep: HomeMovingProgressPostTaskType.Step1,
      oldHomeDetail: null,
      newHomeDetail: null,
      isInBuilding: false,
      isCanMoveInBuilding: false,
      temptStairsTransportStep1: null,
      furniture: null,
      locations: null,

      setLoadingPostTask: (loadingPostTask: boolean) =>
        set({ loadingPostTask: loadingPostTask }),
      setAddress: (address: IAddress) => set({ address: address }),
      setDuration: (duration: number) => set({ duration: duration }),
      setRequirements: (requirements: Requirement[]) =>
        set({ requirements: requirements }),
      setIsPremium: (isPremium: boolean) => set({ isPremium: isPremium }),
      setIsAutoChooseTasker: (isAutoChooseTasker: boolean) =>
        set({ isAutoChooseTasker: isAutoChooseTasker }),
      setIsFavouriteTasker: (isFavouriteTasker: boolean) =>
        set({ isFavouriteTasker: isFavouriteTasker }),
      setGender: (gender: string) => set({ gender: gender }),
      setAddons: (addons: IAddons[]) => set({ addons: addons }),
      setPet: (pet: any) => set({ pet: pet }),
      setDateTime: (date?: IDate) => set({ date: date }),
      setSchedule: (schedule?: string[]) => set({ schedule: schedule }),
      setNote: (note: string) => set({ note: note }),
      setIsApplyNoteForAllTask: (isApplyNoteForAllTask: boolean) =>
        set({ isApplyNoteForAllTask: isApplyNoteForAllTask }),
      setPrice: (price?: Maybe<IPrice>) => set({ price: price }),
      setHomeNumber: (homeNumber: string) => set({ homeNumber: homeNumber }),
      setPaymentMethod: (paymentMethod: any) =>
        set({ paymentMethod: paymentMethod }),
      setPromotion: (promotion: any) => set({ promotion: promotion }),
      setLoadingPrice: (isLoadingPrice: boolean) =>
        set({ isLoadingPrice: isLoadingPrice }),
      setService: (service: IService) => set({ service: service }),
      setWeekdays: (weekdays?: number[]) => set({ weekdays: weekdays }),
      setIsFirstOpen: () => set({ isFirstOpen: false }),
      setDetailOfficeCleaning: (detail: IOptionAreaOfficeCleaning) =>
        set({ detailOfficeCleaning: detail }),
      setVatInfo: (vatInfo: IVatInfo) => set({ vatInfo: vatInfo }),
      setIsUpdateVatInfoToUser: (isUpdateVatInfoToUser: boolean) =>
        set({ isUpdateVatInfoToUser: isUpdateVatInfoToUser }),
      setForceTasker: (forceTasker: Maybe<IUser>) =>
        set({ forceTasker: forceTasker }),
      setDateOptions: (dateOptions: IDate[]) =>
        set({ dateOptions: dateOptions }),

      // Home moving-specific setters
      setCurrentStep: (step: HomeMovingProgressPostTaskType) =>
        set({ currentStep: step }),
      setPassStep: (step: HomeMovingProgressPostTaskType) =>
        set({ passStep: step }),
      setHomeDetail: ({
        step,
        homeDetail,
      }: {
        step: HomeMovingProgressPostTaskType;
        homeDetail: IHomeDetail;
      }) => {
        switch (step) {
          case HomeMovingProgressPostTaskType.Step1:
            set({ oldHomeDetail: homeDetail });
            break;
          case HomeMovingProgressPostTaskType.Step2:
            set({ newHomeDetail: homeDetail });
            break;

          default:
            break;
        }
      },
      setOldHomeDetail: (oldHomeDetail: IHomeDetail) =>
        set({ oldHomeDetail: oldHomeDetail }),
      setNewHomeDetail: (newHomeDetail: IHomeDetail) =>
        set({ newHomeDetail: newHomeDetail }),
      setIsInBuilding: (isInBuilding: IDetailHomeMoving['isInBuilding']) =>
        set({ isInBuilding: isInBuilding }),
      setIsCanMoveInBuilding: (
        isCanMoveInBuilding: IHomeTypeHomeMoving['isCanMoveInBuilding'],
      ) => set({ isCanMoveInBuilding: isCanMoveInBuilding }),
      setTemptStairsTransportStep1: (payload?: Maybe<IOptionHomeDetail>) =>
        set({ temptStairsTransportStep1: payload }),
      setFurniture: (furniture: IFurnitureItem[]) =>
        set({ furniture: furniture }),
      addLocation: (location: IUserLocation) => {
        const store = usePostTaskStore.getState;
        const newLocations = [...(store().locations || [])];
        const isExits = newLocations?.some(
          (locationItem) => locationItem.address === location.address,
        );
        if (!isExits) {
          newLocations.push(location);
          set({ locations: newLocations });
        }
      },
      resetHomeMovingState: () =>
        set({
          price: null,
          currentStep: HomeMovingProgressPostTaskType.Step1,
          passStep: HomeMovingProgressPostTaskType.Step1,
          oldHomeDetail: null,
          newHomeDetail: null,
          isInBuilding: false,
          isCanMoveInBuilding: false,
          temptStairsTransportStep1: null,
          furniture: null,
          locations: null,
        }),
      resetState: () =>
        set({
          address: {},
          duration: 0,
          requirements: [],
          isPremium: false,
          isAutoChooseTasker: true,
          isFavouriteTasker: false,
          gender: '',
          pet: '',
          addons: [],
          date: null,
          schedule: [],
          isEnabledSchedule: false,
          note: '',
          isApplyNoteForAllTask: false,
          homeNumber: '',
          price: null,
          service: null,
          paymentMethod: null,
          promotion: null,
          isLoadingPrice: false,
          loadingPostTask: false,
          weekdays: [],
          isFirstOpen: true,
          detailOfficeCleaning: null,
          vatInfo: null,
          isUpdateVatInfoToUser: false,
          // Reset home moving state as well
          currentStep: HomeMovingProgressPostTaskType.Step1,
          passStep: HomeMovingProgressPostTaskType.Step1,
          oldHomeDetail: null,
          newHomeDetail: null,
          isInBuilding: false,
          isCanMoveInBuilding: false,
          temptStairsTransportStep1: null,
          furniture: null,
          locations: null,
        }),
    }),
    {
      name: 'home-moving-storage',
      storage: createJSONStorage(() => AppStorage.zustandStorage),
      version: 1,
      partialize: (state) => ({ isFirstOpen: state.isFirstOpen }),
    },
  ),
);
