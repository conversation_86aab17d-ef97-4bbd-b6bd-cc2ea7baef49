import { i18n } from '@btaskee/design-system';

import en from './localization/en.json';
import id from './localization/id.json';
import ko from './localization/ko.json';
import ms from './localization/ms.json';
import th from './localization/th.json';
import vi from './localization/vi.json';

const dynamicTranslations = {
  en: en,
  id: id,
  ko: ko,
  ms: ms,
  th: th,
  vi: vi,
};

Object.entries(dynamicTranslations).forEach(([lang, resources]) => {
  i18n.addResourceBundle(lang, 'homeMoving', resources);
});
