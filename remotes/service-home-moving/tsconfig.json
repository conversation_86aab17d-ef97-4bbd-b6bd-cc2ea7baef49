{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@components": ["src/components"], "@types": ["src/types"], "@images": ["src/assets/images"], "@screens": ["src/screens"], "@store": ["src/storage"], "@hooks": ["src/hooks"], "@navigation/*": ["src/navigation/*"], "@config": ["src/config"], "@constant": ["src/constant"], "@i18n": ["src/i18n"], "@e2e/*": ["e2e/*"]}}}