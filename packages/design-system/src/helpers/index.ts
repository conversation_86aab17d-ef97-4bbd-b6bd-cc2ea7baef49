/**
 * @description format money
 * @param number
 * @example
 * number: 100000
 * result: 100,000
 */
import { Linking } from 'react-native';
import { find, get, isEmpty } from 'lodash-es';
import { LunarDate } from 'vietnamese-lunar-calendar';

import { i18n } from '../i18n';
import { useAppStore, useSettingsStore } from '../stores';
import {
  IMonthlyOption,
  IObjectText,
  IPricingSub,
  IRequirement,
} from '../types';
import { RouteName } from '../utils';
import {
  COUNTRY_CODE_ID,
  COUNTRY_CODE_MY,
  COUNTRY_CODE_TH,
  COUNTRY_CODE_VN,
  DEFAULT_MONTHLY_OPTIONS,
  DURATIONS_BY_ISO_CODE,
  HOLIDAYS,
  ISO_CODE,
  LOCALES,
  LOCALES_DEFAULT,
  NATIONAL_HOLIDAYS,
  SERVICES,
  SERVICES_SUBSCRIPTION,
} from '../utils/constant';
import { Alert } from './alert.helpers';
import { DateTimeHelpers, ITimezone } from './date-time.helpers';

export * from './aes.helpers';
export * from './alert.helpers';
export * from './animation.helper';
export * from './app';
export * from './config.helpers';
export * from './date-time.helpers';
export * from './device.helper';
export * from './money';
export * from './permissions.helpers';
export * from './post-task.helpers';
export * from './string.helpers';
export * from './task-places-dictionary';
export * from './toast.helpers';

export const formatMoney = (number: number = 0) => {
  // Chỉ giữ lại 2 chữ số phần thập phân
  const decimalPlaces = 2;

  // Tách phần nguyên và phần thập phân
  let [integer, decimal] = number.toString().split('.');

  // Format phần nguyên theo dấu phẩy hàng nghìn
  integer = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Giữ nguyên phần thập phân (không làm tròn)
  if (decimal) {
    decimal = decimal.slice(0, decimalPlaces);
    return `${integer}.${decimal}`;
  }

  // Nếu không có phần thập phân
  return integer;
};

export const unFormatMoney = (money = '0') => {
  return parseFloat(money.replace(/[^0-9-.]/g, ''));
};

/**
 * @description Get isoCode for variable global
 */
export const getIsoCodeGlobal = () => {
  const appStore = useAppStore.getState();
  return appStore.isoCode;
};

/**
 * @description Get locale for variable global
 */
export const getLocaleGlobal: () => LOCALES = () => {
  return LOCALES.vi;
};

// found minute to 15min
export const roundOfNumberMinutes = (minutes: number) => {
  return (Math.ceil(minutes / 5) * 5) % 60;
};

/**
 * @description: get text from Object by locale
 * @param objectText Object
 * {vi, en, ko, th}
 * @param locale String
 * @returns String
 * text.locale or text.en
 */
export const getTextWithLocale = (
  objectText?: IObjectText,
  localeParam?: LOCALES,
) => {
  const text = '';
  if (!objectText) return text;

  const locale = useAppStore.getState().locale;
  const language = localeParam || locale || LOCALES_DEFAULT;

  return get(objectText, language, '')
    ? get(objectText, language, '')
    : get(objectText, LOCALES_DEFAULT, '');
};

// Get finalCost from task: V3 use costDetail, V2 use cost
export const getFinalCost = (costDetail: any) => {
  let final = costDetail?.finalCost || 0;

  // New cost when prepay task is updated
  /**
   * Nếu task có newFinalCost thì lấy newFinalCost
   * Trường hợp task Tết thanh toán bằng Card sẽ thanh toán trước 3 ngày
   */
  if (costDetail?.newFinalCost) {
    const newFinal = costDetail?.newFinalCost || 0;
    if (newFinal && newFinal > final) {
      final = costDetail?.newFinalCost;
    }
  }
  return final;
};

/**
 * @description refactor PhoneNumber with countryCode
 * @param phoneNumber
 * @param countryCode
 * @returns
 * 0987860977 if phoneNumber is 987860977 (countryCode = TH, VN)
 * 0987860977 if phoneNumber is 0987860977 (countryCode = TH, VN)
 */
export const getPhoneNumber = (phoneNumber: string, countryCode: string) => {
  countryCode = String(countryCode);
  let newPhoneNumber = String(phoneNumber);
  // auto add 0 prefix phone number for viet nam, thai land
  const listCountryCode = [
    COUNTRY_CODE_VN,
    COUNTRY_CODE_TH,
    COUNTRY_CODE_ID,
    COUNTRY_CODE_MY,
  ];
  if (listCountryCode.indexOf(countryCode) >= 0 && phoneNumber[0] !== '0') {
    newPhoneNumber = '0' + newPhoneNumber;
  }
  return newPhoneNumber;
};

/**
 * @param account String phone number of truemoney
 * @returns ******1234
 */
export const showTrueMoneyAccount = (account = '') => {
  let result = '';
  const length = account.length;
  if (length > 0) {
    const charcterNeedHide = account
      .substring(0, length - 4)
      .split('')
      .map((e) => '*')
      .join('');
    const charcterNeedShow = account.substring(length - 4, length);
    result = charcterNeedHide + charcterNeedShow;
  }
  return result;
};

/**
 * @desciption Hiển thị thông báo lỗi tương ứng sau khi gọi API
 * @param error
 * @returns
 */
export const handleError = (
  error: any,
  locale?: LOCALES,
  onClosed?: () => void,
) => {
  // TODO: để tạm Hải config lại sau
  // const language = locale || getLocaleGlobal();
  // Hiển thị mặc đinh: Có lỗi xảy ra + [Mã lỗi]
  // const objectAlert = {
  //   title: i18n.t('DIALOG_TITLE_INFORMATION'),
  //   message: i18n.t('DIALOG.ERROR_TRY_AGAIN', {
  //     t: error?.code || error?.message || '',
  //   }),
  //   actions: [{ text: i18n.t('CLOSE') }],
  // };
  // if (error?.errorText) {
  //   // Mã lỗi chưa được định nghĩa, show theo message trả về
  //   objectAlert.message = [
  //     {
  //       text: getTextWithLocale(error?.errorText, language),
  //       notUsedI18n: true,
  //     },
  //   ];
  // }

  // Khi đóng alert sẽ chạy function onClosed nếu có
  // if (onClosed) {
  //   objectAlert.onClose = onClosed;
  // }
  return Alert.alert?.open?.({
    title: i18n.t('DIALOG_TITLE_INFORMATION'),
    message: i18n.t('ERROR_TRY_AGAIN', {
      t: error?.code || error?.message || '',
    }),
    actions: [{ text: i18n.t('CLOSE') }],
  });
};

export const validPhoneNumber = (
  phoneNumber: string,
  countryCode = COUNTRY_CODE_VN,
) => {
  if (!phoneNumber || phoneNumber.length > 13) {
    return false;
  }
  let phoneRegex: string | RegExp = '';
  switch (countryCode) {
    case COUNTRY_CODE_VN: // Việt Nam
      //maximum 10 character
      if (phoneNumber.length > 10) {
        return false;
      }
      // check exist fist string is 0
      if (phoneNumber[0].toString() === '0') {
        phoneRegex =
          /^[0][9]\d{8}$|[0][8][1-9]\d{7}$|[0][1][2|6|8|9]\d{8}$|[0][5][2|6|8|9]\d{7}$|[0][3][2-9]\d{7}$|[0][7][0|6|7|8|9]\d{7}$/;
      } else {
        phoneRegex =
          /^[9]\d{8}$|[8][1-9]\d{7}$|[1][2|6|8|9]\d{8}$|[5][2|6|8|9]\d{7}$|[3][2-9]\d{7}$|[7][0|6|7|8|9]\d{7}$/;
      }
      //phoneRegex = /^[0][9]\d{8}$|[0][8][6|8|9]\d{7}$|[0][1][2|6|8|9]\d{8}$/;
      return new RegExp(phoneRegex).test(phoneNumber);
    case COUNTRY_CODE_TH: // Thái Lan
      if (phoneNumber.length > 10) {
        return false;
      }
      // check exist fist string is 0
      if (phoneNumber[0].toString() === '0') {
        phoneRegex = /^[0][6]\d{8}$|[0][8]\d{8}$|[0][9]\d{8}$/;
      } else {
        phoneRegex = /^[6]\d{8}$|[8]\d{8}$|[9]\d{8}$/;
      }
      return new RegExp(phoneRegex).test(phoneNumber);
    case COUNTRY_CODE_ID: // Indonesia
      phoneRegex = /^(\+628|628|08|8)[1-9][0-9]{6,10}$/;
      return new RegExp(phoneRegex).test(phoneNumber);
    case COUNTRY_CODE_MY: // Malaysia
      phoneRegex =
        /^(\+60|60|0)?1[0-9]{1}[0-9]{7,8}$|^(\+60|60)?[1-9][0-9]{1,2}-?[0-9]{7}$/;
      return new RegExp(phoneRegex).test(phoneNumber);
    default:
      return false;
  }
};
/**
 * Trả về ngày lễ, tết của Việt Name
 * @param date
 * @returns
 */
export const getLunaHoliday = (paramDate: any, timezone: ITimezone) => {
  const { date, month } = new LunarDate(
    DateTimeHelpers.toDateTz({ date: paramDate, timezone }).toDate(),
  );
  return (
    NATIONAL_HOLIDAYS.find((d) => d.day === date && d.month === month)?.info ||
    null
  );
};

export const checkHoliday = (paramDate: any, timezone: ITimezone) => {
  const month = DateTimeHelpers.getMonth({ date: paramDate, timezone }) + 1; // Lấy tháng (cộng 1 vì getMonth() trả về từ 0-11)
  const day = DateTimeHelpers.getDate({ date: paramDate, timezone });

  const holiday =
    HOLIDAYS.find((d) => d.day === day && d.month === month) || null;
  if (holiday) {
    return holiday;
  }
  return getLunaHoliday(paramDate, timezone);
};

/**
 * @description Get isoCode for variable global
 */
export const getDurationByCountry = () => {
  return DURATIONS_BY_ISO_CODE[getIsoCodeGlobal() || ISO_CODE.VN];
};

export const getDiscountMonthByCity = (cityName?: string) => {
  const { settings } = useSettingsStore.getState();
  if (!cityName) {
    return null;
  }
  if (
    !settings?.subscriptionSetting?.discount ||
    settings?.subscriptionSetting?.discount?.length < 0
  ) {
    return null;
  }
  const discount = find(settings?.subscriptionSetting?.discount, {
    city: cityName,
  });
  return discount?.discountByMonth ? discount.discountByMonth : null;
};

/**
 * @description
 * Ngày trước set cứng monthList trên app  giờ thêm monthlyOptions vào service
 * Thêm hàm này check nếu không có service.monthlyOptions thì trả về set cứng như cũ đảm bảo không bị bug
 * @param
 * monthlyOptions : [{name: '1month',text: {vi: '1 tháng',en: '1 month',ko: '1 개월',th: '1 เดือน',id: '1 bulan'},duration: 1},.....]
 * serviceName : string
 *
 * @note: Hiện tại ở app không dùng text: {vi: '1 tháng',en: '1 month',ko: '1 개월',th: '1 เดือน',id: '1 bulan'} trả về mà tự show bằng localization
 *
 */
export const getMonthListDefaultByService = ({
  monthlyOptions,
  serviceName,
}: {
  monthlyOptions: IMonthlyOption[];
  serviceName: SERVICES_SUBSCRIPTION;
}) => {
  if (isEmpty(monthlyOptions)) {
    return (
      DEFAULT_MONTHLY_OPTIONS[serviceName] || DEFAULT_MONTHLY_OPTIONS.DEFAULT
    );
  }
  return monthlyOptions;
};

export const getIncreasePiceTet = (pricing: IPricingSub[]) => {
  if (isEmpty(pricing)) {
    return 0;
  }

  let increasePrice = 0;
  pricing?.forEach((price) => {
    const increaseReasonArray = price?.costDetail?.increaseReasons;

    if (!isEmpty(increaseReasonArray)) {
      const specialFee = increaseReasonArray?.find(
        (reason) => reason?.key === 'SPECIAL_TIME' && reason.value > 0,
      );
      if (specialFee?.value && specialFee?.value > 0) {
        increasePrice += specialFee?.value;
      }
    }
  });
  return increasePrice;
};

export const validEmail = (email?: string) => {
  if (!email) {
    return false;
  }
  const emailRegEx =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  const emailRegExExtension = /.con$/;
  const resultSyntax = new RegExp(emailRegEx).test(email);
  const resultExtension = new RegExp(emailRegExExtension).test(email);
  return resultSyntax && !resultExtension;
};

/**
 * @description return capitalize weekday
 * @param str
 * @returns String weekday
 * str: thứ 2
 * result: Thứ 2
 */
export const capitalize = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * @description format number
 * @param number
 * @example
 * number: 1
 * result: 01
 */
export const formatNumber = (number = 0) => {
  if (Number(number) > 0 && Number(number) <= 9) {
    return `0${number}`;
  }
  return number;
};

export const getRouteNameOfService = (serviceName: SERVICES) => {
  if (!serviceName) {
    return null;
  }

  const isoCode = getIsoCodeGlobal();

  if (isoCode === ISO_CODE.TH && serviceName === SERVICES.SOFA) {
    return RouteName.SofaCleaningThailandService;
  }

  const listRouteName = {
    [SERVICES.CLEANING]: RouteName.CleaningService,
    [SERVICES.CLEANING_SUBSCRIPTION]: RouteName.CleaningSubscriptionService,
    [SERVICES.DEEP_CLEANING]: RouteName.DeepCleaningService,
    [SERVICES.AIR_CONDITIONER]: RouteName.AirConditionerService,
    [SERVICES.ELDERLY_CARE]: RouteName.ElderlyCareService,
    [SERVICES.PATIENT_CARE]: RouteName.PatientCareService,
    [SERVICES.CHILD_CARE]: RouteName.ChildCareService,
    [SERVICES.OFFICE_CLEANING]: RouteName.OfficeCleaningService,
    [SERVICES.WASHING_MACHINE]: RouteName.WashingMachineService,
    [SERVICES.WATER_HEATER]: RouteName.WaterHeaterService,
    [SERVICES.DISINFECTION_SERVICE]: RouteName.DisinfectionService,
    [SERVICES.HOME_COOKING]: RouteName.HomeCookingService,
    [SERVICES.OFFICE_CARPET_CLEANING]: RouteName.OfficeCarpetCleaningService,
    [SERVICES.INDUSTRIAL_CLEANING]: RouteName.IndustrialCleaningService,
    [SERVICES.SOFA]: RouteName.SofaCleaningService,
    [SERVICES.MASSAGE]: RouteName.MassageService,
    [SERVICES.IRONING]: RouteName.IroningService,
    [SERVICES.LAUNDRY]: RouteName.LaundryService,
    [SERVICES.HOME_MOVING]: RouteName.HomeMovingService,
  };

  return (listRouteName as any)[serviceName] || null;
};

/**
 * Rounds 2 decimal places
 * @param rating:4.35344
 * @returns number:4.35
 */
export const getAvgRating = (
  taskerProfile,
  numberOfTaskCanSeeRatingTasker = 20,
) => {
  // only show 5 stars for tasker with less than 20 tasks done
  const taskDone =
    taskerProfile?.taskDone || taskerProfile?.numberOfTaskDone || 0;
  let avgRating = taskerProfile?.avgRating || 0;

  // get avgRating with numberOfTaskCanSeeRatingTasker
  if (taskDone <= numberOfTaskCanSeeRatingTasker) {
    avgRating = 5;
  }

  return Number(avgRating).toPrecision(2);
};

/**
 * @param requirements [{type: '', cost: '', text: {vi:'', en: '', ko: '', th: ''}}]
 *
 * @returns string
 */
export const getTaskRequirements = (requirements: IRequirement[] = []) => {
  if (!requirements) return '';
  const requirementResult: string[] = [];

  const resTypeLocalization = {
    1: 'REQUIREMENTS_COOKING',
    2: 'REQUIREMENTS_IRON',
    3: 'BRING_TO_TOOLS',
    4: 'REQUIREMENTS_2_SHOTS_VACCINE',
    5: 'REQUIREMENTS_NEGATIVE_TEST_COVID',
    6: 'REQUIREMENTS_CLEANING_GLASS',
    7: 'REQUIREMENTS_VACUUMING_CARPET',
    8: 'REQUIREMENTS_CLEANING_FRIDGE',
  };

  requirements.map((data: IRequirement) => {
    // If requirement no text, check type
    if (
      data?.type &&
      !data?.text &&
      resTypeLocalization?.[data.type as keyof typeof resTypeLocalization]
    ) {
      return requirementResult.push(
        i18n.t(
          resTypeLocalization[data.type as keyof typeof resTypeLocalization],
        ),
      );
    }

    if (data?.text) {
      requirementResult.push(getTextWithLocale(data?.text));
    }
  });
  return requirementResult.join(', ');
};

export const openUrl = (url: string, callback?: () => void) => {
  if (!url) {
    return;
  }
  Linking.canOpenURL(url)
    .then((supported) => {
      if (!supported) {
        // console.log("Can't handle url: " + url);
        callback && callback();
      } else {
        return Linking.openURL(url);
      }
    })
    .catch((err) => {
      // console.log('An error occurred', err);
      callback && callback();
    });
};

/**
 * @description get giờ từ giờ
 * ví dụ: input "03:25:12" => output 3
 */
export const getHourFromTime = (time?: string, timezone?: ITimezone) => {
  if (!time) return 0;
  return DateTimeHelpers.getHour({
    date: DateTimeHelpers.toDateTz({
      date: time,
      format: 'HH:mm:ss' as any,
      timezone,
    }),
    timezone: timezone || DateTimeHelpers.getTzDevice(),
  });
};
