import { Platform } from 'react-native';

import {
  flagIndonesia,
  flag<PERSON><PERSON><PERSON>ia,
  flagThailand,
  flagVietnam,
  icChooseTasker,
  icFavTasker,
  IconAssets,
  iconChooseGender,
  icPet,
} from '../assets';
import { HomeMovingProgressPostTaskType } from '../types/home-moving';

// Khi thêm service mới nhớ cập nhật field SERVICE_TEXT_TRACKING
export enum SERVICES {
  CLEANING = 'CLEANING',
  CLEANING_SUBSCRIPTION = 'CLEANING_SUBSCRIPTION',
  AIR_CONDITIONER = 'AIR_CONDITIONER_SERVICE',
  LAUNDRY = 'LAUNDRY',
  DEEP_CLEANING = 'DEEP_CLEANING',
  HOME_COOKING = 'HOME_COOKING',
  HOUSE_KEEPING = 'HOUSE_KEEPING',
  GROCERY_ASSISTANT = 'GO_MARKET',
  UPHOLSTERY = 'UPHOLSTERY_SERVICE',
  DISINFECTION_SERVICE = 'DISINFECTION_SERVICE',
  SOFA = 'SofaCleaning',
  ELDERLY_CARE = 'ELDERLY_CARE',
  PATIENT_CARE = 'PATIENT_CARE',
  GROCERY_ASSISTANT_OLD = 'GO_MARKET_OLD',
  ELDERLY_CARE_SUBSCRIPTION = 'ELDERLY_CARE_SUBSCRIPTION',
  PATIENT_CARE_SUBSCRIPTION = 'PATIENT_CARE_SUBSCRIPTION',
  CHILD_CARE = 'CHILD_CARE',
  CHILD_CARE_SUBSCRIPTION = 'CHILD_CARE_SUBSCRIPTION',
  OFFICE_CLEANING = 'OFFICE_CLEANING',
  OFFICE_CLEANING_SUBSCRIPTION = 'OFFICE_CLEANING_SUBSCRIPTION',
  WASHING_MACHINE = 'WASHING_MACHINE',
  HOME_MOVING = 'HOME_MOVING',
  WATER_HEATER = 'WATER_HEATER',
  OFFICE_CARPET_CLEANING = 'OFFICE_CARPET_CLEANING',
  MASSAGE = 'MASSAGE',
  INDUSTRIAL_CLEANING = 'INDUSTRIAL_CLEANING',
  BEAUTY_CARE = 'BEAUTY_CARE',
  IRONING = 'IRONING',
  MAKEUP = 'MAKEUP',
  HAIR_STYLING = 'HAIR_STYLING',
  NAIL = 'NAIL',
}

// --------------
export const isIOS = Boolean(Platform.OS.toLowerCase() === 'ios');
export const isAndroid = Boolean(Platform.OS.toLowerCase() === 'android');

export const COUNTRY_CODE_VN = '+84';
export const COUNTRY_CODE_TH = '+66';
export const COUNTRY_CODE_ID = '+62';
export const COUNTRY_CODE_MY = '+60';

export enum ISO_CODE {
  VN = 'VN',
  TH = 'TH',
  ID = 'ID',
  MY = 'MY',
}

export enum USER_STATUS {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DISABLED = 'DISABLED',
  LOCKED = 'LOCKED',
}

export enum RANK_NAME {
  MEMBER = 'MEMBER',
  SILVER = 'SILVER',
  GOLD = 'GOLD',
  PLATINUM = 'PLATINUM',
}

export enum LOCALES {
  vi = 'vi',
  en = 'en',
  th = 'th',
  ko = 'ko',
  id = 'id',
  ms = 'ms',
}

export const LOCALES_DEFAULT = LOCALES.en;

export enum GENDER {
  male = 'MALE',
  female = 'FEMALE',
}

export enum CountryCode {
  vn = '+84',
  th = '+66',
  id = '+62',
  my = '+60',
}

export const COUNTRIES = [
  {
    name: 'CT_VIET_NAM',
    isoCode: ISO_CODE.VN,
    countryCode: CountryCode.vn,
    flag: flagVietnam,
  },
  {
    name: 'CT_THAI_LAND',
    isoCode: ISO_CODE.TH,
    countryCode: CountryCode.th,
    flag: flagThailand,
  },
  {
    name: 'CT_INDONESIA',
    isoCode: ISO_CODE.ID,
    countryCode: CountryCode.id,
    flag: flagIndonesia,
  },
  {
    name: 'CT_MALAYSIA',
    isoCode: ISO_CODE.MY,
    countryCode: CountryCode.my,
    flag: flagMalaysia,
  },
];

export enum HomeType {
  Home = 'HOME',
  Apartment = 'APARTMENT',
  Villa = 'VILLA',
  HomeOffice = 'HOME_OFFICE',
  OfficeBuilding = 'OFFICE_BUILDING',
}

export const ESTIMATED_TIME_POST_TASK_MINUTES = 10; // time for users to post task
export const MIN_POST_TASK_TIME = 60;
export const BEFORE_HOUR_POST_TASK_CHOOSE_TASKER = 3; // Khi tự chọn người làm sẽ post task trước 3 tiếng

export const HOLIDAYS = [
  {
    day: 1,
    month: 1,
  },
  {
    day: 30,
    month: 4,
  },
  {
    day: 1,
    month: 5,
  },
  {
    day: 2,
    month: 9,
  },
];

/**
 * Hiển thị ngày âm lịch trong dịp AL
 */
export const NATIONAL_HOLIDAYS = [
  {
    day: 20,
    month: 12,
    info: '20 AL',
  },
  {
    day: 21,
    month: 12,
    info: '21 AL',
  },
  {
    day: 22,
    month: 12,
    info: '22 AL',
  },
  {
    day: 23,
    month: 12,
    info: '23 AL',
  },
  {
    day: 24,
    month: 12,
    info: '24 AL',
  },
  {
    day: 25,
    month: 12,
    info: '25 AL',
  },
  {
    day: 26,
    month: 12,
    info: '26 AL',
  },
  {
    day: 27,
    month: 12,
    info: '27 AL',
  },
  {
    day: 28,
    month: 12,
    info: '28 AL',
  },
  {
    day: 29,
    month: 12,
    info: '29 AL',
  },
  {
    day: 30,
    month: 12,
    info: '30 AL',
  },

  {
    day: 1,
    month: 1,
    info: '1/1 Tết',
  },
  {
    day: 2,
    month: 1,
    info: '2 AL',
  },
  {
    day: 3,
    month: 1,
    info: '3 AL',
  },
  {
    day: 4,
    month: 1,
    info: '4 AL',
  },

  {
    day: 5,
    month: 1,
    info: '5 AL',
  },
  {
    day: 6,
    month: 1,
    info: '6 AL',
  },
  {
    day: 7,
    month: 1,
    info: '7 AL',
  },
  {
    day: 8,
    month: 1,
    info: '8 AL',
  },
  {
    day: 9,
    month: 1,
    info: '9 AL',
  },
  {
    day: 10,
    month: 1,
    info: '10 AL',
  },
];

export const OPTIONAL_CHOOSE_TASKER = 'ChooseTasker';
export const OPTIONAL_CHOOSE_FAV_TASKER = 'ChooseFavoriteTasker';
export const OPTIONAL_CHOOSE_PET = 'ChoosePet';
export const OPTIONAL_CHOOSE_GENDER = 'ChooseGender';
export const GENDER_LIST = [GENDER.male, GENDER.female];

export const OPTIONAL_BOOKING_TASK = [
  {
    optionalName: OPTIONAL_CHOOSE_TASKER,
    icon: 'choose-tasker',
    label: 'PT1_DETAIL_OPTION_ITEM_CHOOSE_MANUAL',
    image: icChooseTasker,
  },
  {
    optionalName: OPTIONAL_CHOOSE_FAV_TASKER,
    label: 'PT1_DETAIL_OPTION_ITEM_FAV_TASKER',
    image: icFavTasker,
  },
  {
    optionalName: OPTIONAL_CHOOSE_PET,
    label: 'PT1_DETAIL_OPTION_HAVE_PET',
    image: icPet,
  },
  {
    optionalName: OPTIONAL_CHOOSE_GENDER,
    label: 'BOOKING_STEP_2.CHOOSE_GENDER',
    image: iconChooseGender,
  },
];

export enum AddonsName {
  Pet = 'PET',
}

export const INCREASE_FEE_KEY = {
  EMERGENCY: 'EMERGENCY_TASK_FEE',
  SHIP: 'SHIP_FEE',
  WEEKEND: 'WEEKEND_FEE',
};

export const HOUSE_TYPE = [
  {
    key: HomeType.Home,
    value: HomeType.Home,
    label: 'PT1_MAP_POPUP_HOME_TYPE_HOME_DESCRIPTION_TITLE',
    placeholder: 'SV_DC_SCR1_MAP_POPUP_VILLA_DESCRIPTION_HOLDER',
  },
  {
    key: HomeType.Apartment,
    value: HomeType.Apartment,
    label: 'PT1_MAP_POPUP_HOME_TYPE_ITEM_APARTMENT',
    placeholder: 'CHOSE_HOUSE_TYPE_APARTMENT_PLACE_HOLDER',
  },
  {
    key: HomeType.Villa,
    value: HomeType.Villa,
    label: 'PT1_MAP_POPUP_HOME_TYPE_ITEM_VILLA',
    placeholder: 'SV_DC_SCR1_MAP_POPUP_VILLA_DESCRIPTION_HOLDER',
  },
];

export const COMPANY_TYPE = [
  {
    key: HomeType.HomeOffice,
    value: HomeType.HomeOffice,
    label: 'PT1_MAP_POPUP_OFFICE_TYPE_ITEM',
    placeholder: 'SV_DC_SCR1_MAP_POPUP_VILLA_DESCRIPTION_HOLDER',
  },
  {
    key: HomeType.OfficeBuilding,
    value: HomeType.OfficeBuilding,
    label: 'PT1_MAP_POPUP_OFFICE_TYPE_ITEM',
    placeholder: 'CHOSE_HOUSE_TYPE_APARTMENT_PLACE_HOLDER',
  },
];

export enum ApiResultStatus {
  SUCCESS = 'success',
  ERROR = 'error',
}

export let undefinedValue: undefined;

export enum API_RESULT_STATUS {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
}

export enum API_ERROR_CODE {
  // Client-side errors
  MISSING_ISO_CODE = 'MISSING_ISO_CODE',
  CLIENT_NOT_INITIALIZED = 'API_CLIENT_NOT_INITIALIZED',
  INVALID_URL = 'INVALID_URL',
  REQUEST_CANCELLED = 'REQUEST_CANCELLED',
  REQUEST_TIMEOUT = 'ECONNABORTED',
  NETWORK_ERROR = 'ERR_NETWORK',
  OFFLINE = 'OFFLINE',

  // Server-side errors
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  GONE = 410,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,

  // Server errors
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
}

export const DURATIONS_BY_ISO_CODE = {
  VN: [
    { duration: 2, area: '55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '85m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '105m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
  TH: [
    { duration: 2, area: '35m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '35-55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '55-100m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
  ID: [
    { duration: 2, area: '55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '85m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '105m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
  MY: [
    { duration: 2, area: '55m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 3, area: '85m²', rooms: 'NUMBER_OF_ROOMS' },
    { duration: 4, area: '105m²', rooms: 'NUMBER_OF_ROOMS' },
  ],
};

export enum SERVICES_SUBSCRIPTION {
  PATIENT_CARE_SUBSCRIPTION = SERVICES.PATIENT_CARE_SUBSCRIPTION,
  CLEANING_SUBSCRIPTION = SERVICES.CLEANING_SUBSCRIPTION,
  CHILD_CARE_SUBSCRIPTION = SERVICES.CHILD_CARE_SUBSCRIPTION,
  ELDERLY_CARE_SUBSCRIPTION = SERVICES.ELDERLY_CARE_SUBSCRIPTION,
  OFFICE_CLEANING_SUBSCRIPTION = SERVICES.OFFICE_CLEANING_SUBSCRIPTION,
}

export const DEFAULT_MONTHLY_OPTIONS = {
  [SERVICES_SUBSCRIPTION.PATIENT_CARE_SUBSCRIPTION]: [
    { duration: 1 },
    { duration: 2 },
    { duration: 3 },
    { duration: 4 },
  ],
  [SERVICES_SUBSCRIPTION.CLEANING_SUBSCRIPTION]: [
    { duration: 1 },
    { duration: 2 },
    { duration: 3 },
    { duration: 12 },
  ],
  [SERVICES_SUBSCRIPTION.CHILD_CARE_SUBSCRIPTION]: [
    { duration: 1 },
    { duration: 2 },
    { duration: 3 },
    { duration: 4 },
  ],
  [SERVICES_SUBSCRIPTION.ELDERLY_CARE_SUBSCRIPTION]: [
    { duration: 1 },
    { duration: 2 },
    { duration: 3 },
  ],
  [SERVICES_SUBSCRIPTION.OFFICE_CLEANING_SUBSCRIPTION]: [
    { duration: 1 },
    { duration: 2 },
    { duration: 3 },
    { duration: 4 },
  ],
  ['DEFAULT']: [
    { duration: 1 },
    { duration: 2 },
    { duration: 3 },
    { duration: 4 },
  ],
};
export enum PAYMENT_METHOD {
  card = 'CARD', // Visa/Master
  bankTransfer = 'BANK_TRANSFER', // ATM/InternetBanking
  credit = 'CREDIT', // bpay
  momo = 'MOMO',
  directTransfer = 'DIRECT_TRANSFER',
  cash = 'CASH',
  zaloPay = 'ZALO_PAY',
  grabPayByMoca = 'GRAB_PAY_BY_MOCA',
  promptPay = 'PROMPT_PAY',
  trueMoney = 'TRUE_MONEY',
  shopeePay = 'SHOPEE_PAY',
  tiki = 'TIKI',
  vnPay = 'VN_PAY',
  idGoPay = 'GO_PAY',
  idQRIS = 'QRIS',
  idDANA = 'DANA',
  vietQR = 'VIET_QR',
  virtualAccount = 'VIRTUAL_ACCOUNT',
  kredivo = 'KREDIVO',
  bPayBusiness = 'BPAY_BUSINESS',
}

export enum TYPE_OF_PAYMENT {
  topUp = 'topUp',
  subscription = 'subscription',
  bookTask = 'bookTask',
  recharge = 'recharge',
  comboVoucher = 'comboVoucher',
}

export enum PREPAYMENT {
  promptPay = PAYMENT_METHOD.promptPay,
  trueMoney = PAYMENT_METHOD.trueMoney,
  shopeePay = PAYMENT_METHOD.shopeePay,
  zaloPay = PAYMENT_METHOD.zaloPay,
  momo = PAYMENT_METHOD.momo,
  tiki = PAYMENT_METHOD.tiki,
  vnPay = PAYMENT_METHOD.vnPay,
  credit = PAYMENT_METHOD.credit,
  idGoPay = PAYMENT_METHOD.idGoPay,
  idQRIS = PAYMENT_METHOD.idQRIS,
  idDANA = PAYMENT_METHOD.idDANA,
  vietQR = PAYMENT_METHOD.vietQR,
  kredivo = PAYMENT_METHOD.kredivo,
}

export const PAYMENT_METHOD_LIST = [
  {
    name: PAYMENT_METHOD.card,
    label: 'PAYMENT_METHOD_CARD',
    icon: require('../assets/images/payment/logo-visa.png'),
  },
  {
    name: PAYMENT_METHOD.bankTransfer,
    label: 'PAYMENT_METHOD_BANK_TRANSFER',
    icon: require('../assets/images/payment/logo-atm.png'),
  },
  {
    name: PAYMENT_METHOD.credit,
    label: 'PAYMENT_METHOD_CREDIT',
    icon: require('../assets/images/payment/logo-bpay.png'),
  },
  {
    name: PAYMENT_METHOD.momo,
    label: 'PAYMENT_METHOD_MOMO',
    icon: require('../assets/images/payment/logo-momo.png'),
  },
  {
    name: PAYMENT_METHOD.directTransfer,
    label: 'PAYMENT_METHOD_DIRECT_TRANSFER',
    icon: require('../assets/images/payment/logo-direct-transfer.png'),
  },
  {
    name: PAYMENT_METHOD.cash,
    label: 'PAYMENT_METHOD_DIRECT_CASH',
    icon: require('../assets/images/payment/logo-cash.png'),
  },
  {
    name: PAYMENT_METHOD.zaloPay,
    label: 'PAYMENT_METHOD_ZALO_PAY',
    icon: require('../assets/images/payment/logo-zalo-pay-new.png'),
  },
  {
    name: PAYMENT_METHOD.promptPay,
    label: 'PAYMENT_METHOD_PROMT_PAY',
    icon: require('../assets/images/payment/logo-prompt-pay.png'),
  },
  {
    name: PAYMENT_METHOD.trueMoney,
    label: 'PAYMENT_METHOD_TRUE_MONEY',
    value: PAYMENT_METHOD.trueMoney,
    icon: require('../assets/images/payment/logo-truemoney.png'),
  },
  {
    name: PAYMENT_METHOD.shopeePay,
    label: 'PAYMENT_METHOD_SHOPEE_PAY',
    icon: require('../assets/images/payment/logo-shopee-pay.png'),
  },
  {
    name: PAYMENT_METHOD.tiki,
    label: 'PAYMENT_METHOD_TIKI',
    value: PAYMENT_METHOD.tiki,
    icon: require('../assets/images/payment/logo-bpay.png'),
  },
  {
    name: PAYMENT_METHOD.vnPay,
    label: 'PAYMENT_METHOD_VN_PAY',
    icon: require('../assets/images/payment/logo-vn-pay.png'),
  },
  {
    name: PAYMENT_METHOD.idQRIS,
    label: 'PAYMENT_METHOD_QRIS',
    icon: require('../assets/images/payment/logo-qris.png'),
  },
  {
    name: PAYMENT_METHOD.idDANA,
    label: 'PAYMENT_METHOD_DANA',
    value: PAYMENT_METHOD.idDANA,
    icon: require('../assets/images/payment/logo-dana.png'),
  },
  {
    name: PAYMENT_METHOD.vietQR,
    label: 'PAYMENT_METHOD_VIET_QR',
    icon: require('../assets/images/payment/logo-viet-qr.png'),
  },
  {
    name: PAYMENT_METHOD.virtualAccount,
    label: 'PAYMENT_METHOD_VIRTUAL_ACCOUNT',
    value: PAYMENT_METHOD.virtualAccount,
    icon: require('../assets/images/bank-logo/id/visa.png'),
  },
  {
    name: PAYMENT_METHOD.kredivo,
    label: 'PAYMENT_METHOD_KREDIVO',
    icon: require('../assets/images/payment/logo-kredivo.png'),
  },
  {
    name: PAYMENT_METHOD.bPayBusiness,
    label: 'PAYMENT_METHOD_BUSINESS_ACCOUNT',
    icon: require('../assets/images/payment/logo-bpay.png'),
  },
];

export enum TASK_STATUS {
  CANCELED = 'CANCELED',
  CONFIRMED = 'CONFIRMED',
  DONE = 'DONE',
  EXPIRED = 'EXPIRED',
  // EXPIRED_REPOSTED: "EXPIRED-REPOSTED",
  POSTED = 'POSTED',
  WAITING_ASKER_CONFIRMATION = 'WAITING_ASKER_CONFIRMATION',
  INACTIVE = 'INACTIVE',
  ACTIVE = 'ACTIVE',
  NEW = 'NEW',
  PROCESSING = 'PROCESSING',
  WAITING_POSTED = 'WAITING_POSTED',
}

export const SOCIAL_BTASKEE_LINK = {
  BLOG: { VN: 'https://blog.btaskee.com/' },
  TWITTER: { VN: 'https://twitter.com/btaskee' },
  FACEBOOK: {
    VN: 'https://www.facebook.com/btaskee',
    TH: 'https://www.facebook.com/btaskeethailand',
  },
  INSTAGRAM: { VN: 'https://www.instagram.com/btaskee' },
  YOUTUBE: { VN: 'https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ' },
  LINE: { TH: 'https://page.line.me/btaskeeth' },
  ZALO: { VN: 'https://zalo.me/887359237910439961' },
  WHATSAPP: {
    ID: 'https://api.whatsapp.com/send/?phone=6281110007590&text&type=phone_number&app_absent=0',
    MY: 'https://api.whatsapp.com/send/?phone=6281110007590&text&type=phone_number&app_absent=0',
  },
};

export const DURATIONS_FULL_TIME = [
  { duration: 4, type: 'POST_TASK_PART_TIME' },
  { duration: 8, type: 'POST_TASK_FULL_TIME' },
];

export enum ActionChatCreateRequest {
  updateDateTime = 'UPDATE_DATE_TIME',
}

export enum REQUIREMENTS_TYPE {
  COOKING = 1,
  IRONING = 2,
  CLEANING_TOOLS = 3,
  CLEANING_GLASS = 6,
  VACUUM_CARPET = 7,
  FRIDGE_CLEANING = 8,
}

export const LIST_PROGRESS_POST_TASK_HOME_MOVING = [
  {
    step: HomeMovingProgressPostTaskType.Step1,
    icon: IconAssets.icLocation,
  },
  {
    step: HomeMovingProgressPostTaskType.Step2,
    icon: IconAssets.icMoving,
  },
  {
    step: HomeMovingProgressPostTaskType.Step3,
    icon: IconAssets.icBox,
  },
  {
    step: HomeMovingProgressPostTaskType.Step4,
    icon: IconAssets.icClock,
  },
];
