import { IDate, ITimezone } from '../helpers';
import { SERVICES } from '../utils';
import { IObjectText, IUserLocation, Maybe } from './index';

export type INameText = {
  name?: string;
  text?: IObjectText;
};

export enum HomeMovingProgressPostTaskType {
  Step1 = 1,
  Step2 = 2,
  Step3 = 3,
  Step4 = 4,
  Step5 = 5,
}

export enum NameHomeTypeHomeMoving {
  apartment = 'APARTMENT',
  house = 'HOME',
}

export enum NameOptionByHomeTypeHomeMoving {
  stairsTransport = 'stairsTransport', //Vận chuyển thang bộ
  garage = 'garage', //Vận chuyển từ hầm xe
  byroad = 'byroad', //Nhà trong hẻm
  elevatorTransport = 'elevatorTransport', //Vận chuyển thang máy
}

export enum NameTypeHouseHomeTypeMoving {
  groundFloor = 'groundFloor', //Nhà trệt
}

export enum NameOptionTypeFurnitureHomeMoving {
  airConditioner = 'airConditioner',
  waterHeater = 'waterHeater',
}

export enum NameOptionTypeWaterHeaterHomeMoving {
  traditionalWaterHeater = 'traditionalWaterHeater',
  tanklessWaterHeater = 'tanklessWaterHeater',
}

export enum NameTypeFurnitureHomeMoving {
  electronic = 'electronic',
  solidFurniture = 'solidFurniture',
  removableFurniture = 'removableFurniture',
}

export enum TypeLocationHomeMoving {
  New = 'NEW',
  Old = 'OLD',
}
export type IHomeMoving = {
  city?: ISettingHomeMovingServiceByCity[];
};

export type ISettingHomeMovingServiceByCity = {
  name?: string;
  homeType?: IHomeTypeHomeMoving[];
  options?: IOptionHomeTypeHomeMoving[];
  furniture?: IFurnitureHomeMoving;
};

export type IOptionHomeTypeHomeMoving = {
  name?: NameOptionByHomeTypeHomeMoving;
  text?: IObjectText;
  options?: INameText[];
};

type IFurnitureHomeMoving = {
  description?: IObjectText;
  text?: IObjectText;
  type?: ITypeFurnitureHomeMoving[];
};

export type ITypeFurnitureHomeMoving = {
  text?: IObjectText;
  description?: IObjectText;
  name?: NameTypeFurnitureHomeMoving;
  options?: IOptionTypeFurnitureHomeMoving[];
};

export type IOptionTypeFurnitureHomeMoving = {
  name?: NameOptionTypeFurnitureHomeMoving;
  text?: IObjectText;
  description?: IObjectText;
  options?: {
    name?: string;
    text?: IObjectText;
    quantity?: number;
  }[];
};

export type IHomeTypeHomeMoving = {
  name?: NameHomeTypeHomeMoving;
  isCanMoveInBuilding?: boolean;
  options?: NameOptionByHomeTypeHomeMoving[];
  text?: IObjectText;
  type?: TypeHomeTypeHomeMoving[];
};

export type TypeHomeTypeHomeMoving = {
  name?: NameTypeHouseHomeTypeMoving;
  price?: number;
  text?: IObjectText;
  description?: IObjectText;
  vehicleQuantity?: number;
  vehicleType?: string;
  options?: OptionTypeHomeTypeHomeMoving[];
  oldHomeCleaning?: IHomeCleaning;
  newHomeCleaning?: IHomeCleaning;
};

type IHomeCleaning = {
  serviceName?: SERVICES;
  numberOfTasker?: number;
  duration?: number;
  bookTaskAfterHours?: number;
};

export type OptionTypeHomeTypeHomeMoving = {
  name?: string;
  price?: number;
  text?: IObjectText;
  vehicleQuantity?: number;
  vehicleType?: string;
  oldHomeCleaning?: IHomeCleaning;
  newHomeCleaning?: IHomeCleaning;
};

export type IHomeDetail = {
  addressDetail?: {
    taskPlace?: ITaskPlace;
    phone?: string;
    contactName?: string;
  } & Pick<
    IUserLocation,
    'address' | 'lat' | 'lng' | 'description' | 'homeType' | 'shortAddress'
  >;
  homeType?: {
    type?: Pick<
      TypeHomeTypeHomeMoving,
      'name' | 'text' | 'oldHomeCleaning' | 'newHomeCleaning' | 'description'
    > & {
      option?: OptionTypeHomeTypeHomeMoving;
    };
  } & Pick<IHomeTypeHomeMoving, 'name' | 'text'>;
  isCleaningRequired?: boolean;
  options?: IOptionHomeDetail[];
  date?: Maybe<IDate>;
  timezone?: ITimezone;
  taskNote?: string;
};

type ITaskPlace = {
  country?: string;
  city?: string;
  district?: string;
  isAddressMaybeWrong?: boolean;
};

export type IOptionHomeDetail = Pick<
  IOptionHomeTypeHomeMoving,
  'name' | 'text'
> & {
  option?: INameText;
  width?: number;
};

export type IDetailHomeMoving = {
  newHomeDetail?: IHomeDetail;
  oldHomeDetail?: IHomeDetail;
  isInBuilding?: boolean;
  furniture?: IFurnitureItem[];
  homeMovingProcess?: IHomeMovingProcess[];
  stepInProgress?: IHomeMovingProcess;
  removableElectronic?: IRemovableElectronic[];
  waitingFeeSetting?: IWaitingFeeSettingHomeMoving;
};

export type IWaitingFeeSettingHomeMoving = {
  waitingFee?: number;
  maxWaitingTime?: number;
};

export type IRemovableElectronic = {
  name?: string;
  quantity?: number;
  text?: IObjectText;
  options?: IRemovableElectronic[];
};

export type IHomeMovingProcess = {
  description?: IObjectText;
  title?: IObjectText;
  status?: string;
  step?: number;
};

export type ITaskDetailHomeMoving = {
  _id?: string;
  status?: string;
  detailHomeMoving?: IDetailHomeMoving;
  taskNote?: string;
  date?: Date;
  description?: string;
  relatedTasks?: any;
};

export type IFurnitureItem = Pick<
  ITypeFurnitureHomeMoving,
  'name' | 'text' | 'description'
> & {
  quantity?: number;
  images?: string[];
  options?: (Pick<
    IOptionTypeFurnitureHomeMoving,
    'name' | 'text' | 'description'
  > & {
    quantity?: number;
    options?: (INameText & {
      quantity?: number;
    })[];
  })[];
};
