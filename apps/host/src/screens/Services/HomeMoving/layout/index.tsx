import React from 'react';
import { useRoute } from '@react-navigation/native';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const HomeMoving = React.lazy(() => import('homeMoving/MainNavigator'));

export function HomeMovingScreen(): React.JSX.Element {
  const route = useRoute();
  return (
    <ErrorBoundary name="HomeMovingScreen">
      <React.Suspense fallback={<LoadingMiniApp />}>
        <HomeMoving {...route.params} />
      </React.Suspense>
    </ErrorBoundary>
  );
}
