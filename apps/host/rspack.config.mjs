import pkg from '@btaskee/sdk';
import * as Repack from '@callstack/repack';
import { ReanimatedPlugin } from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const { getLocalIP, getSharedDependencies } = pkg;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */
export default (env) => {
  const { mode, platform = process.env.PLATFORM } = env;
  const hostIP = getLocalIP();

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@navigation': path.resolve(__dirname, './src/navigation'),
        '@screens': path.resolve(__dirname, './src/screens'),
        '@app': path.resolve(__dirname, './src/App.tsx'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@images': path.resolve(__dirname, './src/assets/images'),
        '@lottie': path.resolve(__dirname, './src/assets/lottie'),
        '@components': path.resolve(__dirname, './src/components'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@types': path.resolve(__dirname, './src/types'),
        '@stores': path.resolve(__dirname, './src/stores'),
      },
    },
    output: {
      uniqueName: 'sas-host',
      path: path.resolve(__dirname, 'dist', platform),
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules(),
        ...Repack.getAssetTransformRules(),
      ],
    },
    plugins: [
      new Repack.RepackPlugin(),
      new ReanimatedPlugin(),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'host',
        dts: false,
        remotes: {
          auth: `auth@http://${hostIP}:9001/${platform}/mf-manifest.json`,
          cleaning: `cleaning@http://${hostIP}:9002/${platform}/mf-manifest.json`,
          payment: `payment@http://${hostIP}:9003/${platform}/mf-manifest.json`,
          voiceChat: `voiceChat@http://${hostIP}:9004/${platform}/mf-manifest.json`,
          airConditioner: `airConditioner@http://${hostIP}:9005/${platform}/mf-manifest.json`,
          deepCleaning: `deepCleaning@http://${hostIP}:9006/${platform}/mf-manifest.json`,
          elderlyCare: `elderlyCare@http://${hostIP}:9007/${platform}/mf-manifest.json`,
          childCare: `childCare@http://${hostIP}:9008/${platform}/mf-manifest.json`,
          community: `community@http://${hostIP}:9009/${platform}/mf-manifest.json`,
          subscriptionCleaning: `subscriptionCleaning@http://${hostIP}:9010/${platform}/mf-manifest.json`,
          taskManagement: `taskManagement@http://${hostIP}:9011/${platform}/mf-manifest.json`,
          patientCare: `patientCare@http://${hostIP}:9012/${platform}/mf-manifest.json`,
          promotion: `promotion@http://${hostIP}:9013/${platform}/mf-manifest.json`,
          childCareSubscription: `childCareSubscription@http://${hostIP}:9015/${platform}/mf-manifest.json`,
          officeCleaning: `officeCleaning@http://${hostIP}:9014/${platform}/mf-manifest.json`,
          elderlyCareSubscription: `elderlyCareSubscription@http://${hostIP}:9016/${platform}/mf-manifest.json`,
          patientCareSubscription: `patientCareSubscription@http://${hostIP}:9018/${platform}/mf-manifest.json`,
          officeCleaningSubscription: `officeCleaningSubscription@http://${hostIP}:9017/${platform}/mf-manifest.json`,
          waterHeater: `waterHeater@http://${hostIP}:9019/${platform}/mf-manifest.json`,
          washingMachine: `washingMachine@http://${hostIP}:9020/${platform}/mf-manifest.json`,
          disinfection: `disinfection@http://${hostIP}:9021/${platform}/mf-manifest.json`,
          homeCooking: `homeCooking@http://${hostIP}:9022/${platform}/mf-manifest.json`,
          officeCarpetCleaning: `officeCarpetCleaning@http://${hostIP}:9023/${platform}/mf-manifest.json`,
          industrialCleaning: `industrialCleaning@http://${hostIP}:9024/${platform}/mf-manifest.json`,
          sofaCleaning: `sofaCleaning@http://${hostIP}:9025/${platform}/mf-manifest.json`,
          massage: `massage@http://${hostIP}:9026/${platform}/mf-manifest.json`,
          sofaCleaningThailand: `sofaCleaningThailand@http://${hostIP}:9027/${platform}/mf-manifest.json`,
          ironing: `ironing@http://${hostIP}:9028/${platform}/mf-manifest.json`,
          homeMoving: `homeMoving@http://${hostIP}:9030/${platform}/mf-manifest.json`,
        },
        shared: getSharedDependencies({ eager: true }),
      }),
      // silence missing @react-native-masked-view optionally required by @react-navigation/elements
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
    ],
  };
};
