# Super App Workspace - Augment Agent Rules

## 🎯 PRIORITY: Always Use @btaskee/design-system First

**MANDATORY RULE**: Before using any React Native components, libraries, or utilities, ALWAYS check if an equivalent exists in `@btaskee/design-system` and use it instead.

## 🔍 Information Gathering - ALWAYS Research First

**BEFORE making any code changes:**

1. **Use codebase-retrieval** to understand existing patterns and implementations
2. **Search for similar components/features** in the codebase before creating new ones
3. **Check design system availability** for components, hooks, and utilities
4. **Understand the full context** of the change before implementing

```tsx
// ✅ ALWAYS RESEARCH FIRST
// 1. Search for existing implementations
// 2. Check @btaskee/design-system for components
// 3. Understand the codebase patterns
// 4. Then implement following established patterns
```

## 🎨 Design System First - Mandatory Usage

### Core Components (MUST USE)
```tsx
// ✅ ALWAYS USE DESIGN SYSTEM COMPONENTS
import { 
  BlockView,         // Instead of View
  CText,            // Instead of Text
  ScrollView,       // Instead of ScrollView  
  FlatList,         // Instead of FlatList
  TouchableOpacity, // Instead of TouchableOpacity
  CTextInput,       // Instead of TextInput
  PrimaryButton,    // Instead of Button
  FastImage,        // Instead of Image
  CustomModal,      // Instead of Modal
} from '@btaskee/design-system';
```

### Design Tokens (MANDATORY)
```tsx
// ✅ ALWAYS USE DESIGN SYSTEM TOKENS
import { 
  ColorsV2,      // PREFERRED color system (not Colors)
  Spacing,       // For margins, padding, gaps
  FontSizes,     // For font sizes
  FontFamily,    // For font families
  BorderRadius,  // For border radius
} from '@btaskee/design-system';

// ✅ CORRECT COLOR USAGE
ColorsV2.orange500        // PRIMARY color
ColorsV2.green500         // SECONDARY color
ColorsV2.neutral800       // BLACK text
ColorsV2.neutralWhite     // WHITE backgrounds
ColorsV2.neutralBackground // BACKGROUND color
ColorsV2.neutral100       // BORDER color
ColorsV2.neutral400       // GREY text

// ✅ CORRECT SPACING USAGE
Spacing.SPACE_04   // 4px
Spacing.SPACE_08   // 8px
Spacing.SPACE_16   // 16px
Spacing.SPACE_24   // 24px
```

## 🏗️ Architecture Patterns - Follow Established Conventions

### State Management
```tsx
// ✅ USE ZUSTAND STORES (preferred)
import { useAppStore } from '@/stores/appStore';

// ✅ USE DESIGN SYSTEM HOOKS
import { useI18n, useAppLoading } from '@btaskee/design-system';

// ❌ AVOID Redux when Zustand is available
```

### File Structure & Naming
```tsx
// ✅ FOLLOW ESTABLISHED PATTERNS
components/
  ComponentName/
    index.tsx          // Main component
    ComponentName.tsx  // Implementation
    styles.ts         // Styles (if needed)
    types.ts          // TypeScript types

// ✅ USE CONSISTENT NAMING
- PascalCase for components
- camelCase for functions/variables
- UPPER_CASE for constants
- kebab-case for file names (when appropriate)
```

## 🧪 Testing - Comprehensive E2E with Detox

### Mandatory Testing Patterns
```tsx
// ✅ ALWAYS USE testID for element selection
<PrimaryButton testID="booking-confirm-button">
  Confirm Booking
</PrimaryButton>

// ✅ NEVER use text selectors in tests
// ❌ WRONG: await element(by.text('Confirm')).tap();
// ✅ CORRECT: await element(by.id('booking-confirm-button')).tap();

// ✅ USE scroll-to-reveal patterns
await waitFor(element(by.id('target-element')))
  .toBeVisible()
  .whileElement(by.id('scroll-container'))
  .scroll(150, 'down'); // Incremental scrolling

// ✅ VALIDATE sequential booking flows
// Address → Service → DateTime → Payment
```

### Performance Targets
- **E2E Test Duration**: 3-5 minutes maximum
- **Incremental Scrolling**: ~150 pixels (not aggressive swipes)
- **State Management**: Use Zustand stores for test state

## 📱 Mobile Development Best Practices

### Package Management
```bash
# ✅ ALWAYS USE PNPM
pnpm install package-name
pnpm add -D dev-package

# ❌ DON'T manually edit package.json
```

### Internationalization
```tsx
// ✅ USE DESIGN SYSTEM I18N
import { useI18n } from '@btaskee/design-system';

const { t } = useI18n('namespace');
const text = t('key');

// ✅ USE getTextWithLocale for static text
import { getTextWithLocale } from '@btaskee/design-system';
```

### Navigation & Routing
```tsx
// ✅ USE NavigationHelper from design system
import { NavigationHelper } from '@btaskee/design-system';

NavigationHelper.navigate('ScreenName', params);
```

## 🔄 Migration Patterns - Modernization Guidelines

### Component Migration
```tsx
// ✅ WHEN MIGRATING COMPONENTS:
// 1. Preserve exact UI design and functionality
// 2. Replace with ColorsV2 colors
// 3. Use Spacing utilities
// 4. Use FontSizes from design system
// 5. Replace Redux with Zustand stores
// 6. Add proper null checks
// 7. Use design system components

// ✅ MIGRATION EXAMPLE
// OLD:
<View style={{ backgroundColor: '#ffffff', padding: 16 }}>
  <Text style={{ fontSize: 16, color: '#000000' }}>Title</Text>
</View>

// NEW:
<BlockView backgroundColor={ColorsV2.neutralWhite} padding={Spacing.SPACE_16}>
  <CText size={FontSizes.SIZE_16} color={ColorsV2.neutral800}>Title</CText>
</BlockView>
```

### Service Creation
```tsx
// ✅ COPY FROM EXISTING SERVICES
// When creating new microservices:
// 1. Copy from existing service structure
// 2. Adapt to new requirements
// 3. Follow established patterns
// 4. Use design system components
```

## 🚫 What NOT to Do

### Avoid Direct React Native Usage
```tsx
// ❌ DON'T USE THESE DIRECTLY
import { 
  View,              // Use BlockView
  Text,              // Use CText
  TextInput,         // Use CTextInput
  Button,            // Use PrimaryButton
  Image,             // Use FastImage
  Modal,             // Use CustomModal
} from 'react-native';

// ❌ DON'T USE HARDCODED VALUES
const styles = {
  container: {
    backgroundColor: '#ffffff',  // Use ColorsV2.neutralWhite
    padding: 16,                 // Use Spacing.SPACE_16
    fontSize: 14,                // Use FontSizes.SIZE_14
  }
};

// ❌ DON'T USE OLD CONSTANTS
import { PRIMARY_COLOR } from './constants';  // Use ColorsV2.orange500
```

### Avoid Anti-Patterns
```tsx
// ❌ DON'T combine locale and country changes
// ✅ KEEP them as separate functionalities

// ❌ DON'T use text selectors in tests
// ✅ ALWAYS use testID-based selection

// ❌ DON'T use aggressive scrolling in tests
// ✅ USE incremental scrolling (~150px)
```

## 🎯 Development Workflow

### Before Starting Any Task
1. **Research** existing implementations with codebase-retrieval
2. **Plan** the approach using established patterns
3. **Check** design system for available components
4. **Follow** migration patterns for modernization
5. **Test** with comprehensive E2E tests

### Code Quality Standards
- **Type Safety**: Use TypeScript strictly
- **Null Checks**: Always add proper null/undefined checks
- **Performance**: Target 3-5 minute E2E test execution
- **Consistency**: Follow established naming and structure patterns
- **Accessibility**: Use design system's built-in accessibility features

## 🎯 This Rule Applies To

- ALL React Native development in this monorepo
- ALL apps in `apps/` directory
- ALL remotes in `remotes/` directory  
- ALL packages that use React Native components
- ALL testing with Detox framework
- ALL component migrations and modernizations

**Remember: Research First, Design System First, Test Thoroughly! 🎨🔍🧪**
